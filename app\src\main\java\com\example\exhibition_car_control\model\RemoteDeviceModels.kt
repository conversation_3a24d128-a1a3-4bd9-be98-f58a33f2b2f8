package com.example.exhibition_car_control.model

import com.google.gson.annotations.SerializedName

/**
 * 远程设备相关数据模型
 */

// ==================== 设备信息模型 ====================

/**
 * BLE设备信息
 */
data class RemoteBleDeviceInfo(
    @SerializedName("address") val address: String,
    @SerializedName("name") val name: String?,
    @SerializedName("rssi") val rssi: Int,
    @SerializedName("deviceType") val deviceType: String,
    @SerializedName("connectionState") val connectionState: String,
    @SerializedName("lastSeen") val lastSeen: Long,
    @SerializedName("services") val services: List<String> = emptyList(),
    @SerializedName("batteryLevel") val batteryLevel: Int? = null,
    @SerializedName("firmwareVersion") val firmwareVersion: String? = null,
    @SerializedName("isOnline") val isOnline: Boolean = false,
    @SerializedName("lastStatusUpdate") val lastStatusUpdate: Long = 0L,
    @SerializedName("status") val status: DeviceStatus? = null
)

/**
 * 设备类型枚举
 */
enum class DeviceType(val code: String, val displayName: String, val icon: String) {
    BUTTON_DEVICE("01", "点云灵动键", "🔘"),
    WIRELESS_CHARGER("02", "无线充电器", "🔌"),
    LAMP_MAIN("03", "主灯", "💡"),
    LAMP_AMBIENT("04", "氛围灯", "🌈"),
    FRAGRANCE_MACHINE("05", "香氛机", "🌸"),
    FAN("06", "风扇", "🌀"),
    UNKNOWN("FF", "未知设备", "❓");

    companion object {
        fun fromCode(code: String): DeviceType {
            return values().find { it.code == code } ?: UNKNOWN
        }
    }
}

/**
 * 连接状态枚举
 */
enum class ConnectionState(val displayName: String) {
    DISCONNECTED("未连接"),
    CONNECTING("连接中"),
    CONNECTED("已连接"),
    DISCONNECTING("断开中"),
    ERROR("连接错误");

    companion object {
        fun fromString(state: String): ConnectionState {
            return values().find { it.name.equals(state, ignoreCase = true) } ?: DISCONNECTED
        }
    }
}

/**
 * 设备状态
 */
data class DeviceStatus(
    @SerializedName("deviceType") val deviceType: String,
    @SerializedName("isOn") val isOn: Boolean,
    @SerializedName("brightness") val brightness: Int? = null,
    @SerializedName("speed") val speed: Int? = null,
    @SerializedName("batteryLevel") val batteryLevel: Int? = null,
    @SerializedName("temperature") val temperature: Float? = null,
    @SerializedName("errorCode") val errorCode: String? = null,
    @SerializedName("lastUpdate") val lastUpdate: Long,
    @SerializedName("rawData") val rawData: List<Int>? = null
)

// ==================== 树莓派连接模型 ====================

/**
 * 树莓派连接信息
 */
data class RaspberryPiConnection(
    val id: String,
    val address: String,
    val name: String,
    val port: Int = 8080,
    val connectionType: ConnectionType,
    val state: ConnectionState,
    val connectedAt: Long? = null,
    val lastHeartbeat: Long? = null,
    val signalStrength: Int? = null,
    val latency: Long? = null,
    val dataTransferred: DataTransferStats = DataTransferStats(),
    val capabilities: List<String> = emptyList(),
    val systemInfo: SystemInfo? = null
)

/**
 * 连接类型
 */
enum class ConnectionType(val displayName: String) {
    BLUETOOTH_CLASSIC("经典蓝牙"),
    WIFI("WiFi连接"),
    USB("USB连接"),
    ETHERNET("以太网连接")
}

/**
 * 数据传输统计
 */
data class DataTransferStats(
    val bytesSent: Long = 0L,
    val bytesReceived: Long = 0L,
    val messagesSent: Long = 0L,
    val messagesReceived: Long = 0L,
    val errors: Long = 0L,
    val lastActivity: Long = 0L
)

/**
 * 系统信息
 */
data class SystemInfo(
    @SerializedName("cpu") val cpuUsage: Float,
    @SerializedName("memory") val memoryUsage: Float,
    @SerializedName("temperature") val temperature: Float,
    @SerializedName("uptime") val uptime: Long,
    @SerializedName("bluetooth") val bluetoothAdapterState: BluetoothAdapterState,
    @SerializedName("connectedDeviceCount") val connectedDeviceCount: Int,
    @SerializedName("availableStorage") val availableStorage: Long? = null,
    @SerializedName("networkInterfaces") val networkInterfaces: List<NetworkInterface> = emptyList()
)

/**
 * 蓝牙适配器状态
 */
data class BluetoothAdapterState(
    @SerializedName("enabled") val enabled: Boolean,
    @SerializedName("discoverable") val discoverable: Boolean,
    @SerializedName("scanning") val scanning: Boolean,
    @SerializedName("address") val address: String? = null,
    @SerializedName("name") val name: String? = null,
    @SerializedName("supportedProfiles") val supportedProfiles: List<String> = emptyList()
)

/**
 * 网络接口信息
 */
data class NetworkInterface(
    @SerializedName("name") val name: String,
    @SerializedName("address") val address: String,
    @SerializedName("type") val type: String,
    @SerializedName("isUp") val isUp: Boolean
)

// ==================== 操作结果模型 ====================

/**
 * 操作结果
 */
data class OperationResult<T>(
    @SerializedName("success") val success: Boolean,
    @SerializedName("data") val data: T? = null,
    @SerializedName("error") val error: OperationError? = null,
    @SerializedName("timestamp") val timestamp: Long = System.currentTimeMillis(),
    @SerializedName("duration") val duration: Long = 0L,
    @SerializedName("requestId") val requestId: String? = null
)

/**
 * 操作错误
 */
data class OperationError(
    @SerializedName("code") val code: String,
    @SerializedName("message") val message: String,
    @SerializedName("type") val type: String,
    @SerializedName("details") val details: String? = null,
    @SerializedName("suggestions") val suggestions: List<String> = emptyList(),
    @SerializedName("retryable") val retryable: Boolean = false
)

/**
 * 错误类型
 */
enum class ErrorType(val displayName: String) {
    NETWORK_ERROR("网络错误"),
    BLUETOOTH_ERROR("蓝牙错误"),
    DEVICE_ERROR("设备错误"),
    PERMISSION_ERROR("权限错误"),
    TIMEOUT_ERROR("超时错误"),
    VALIDATION_ERROR("验证错误"),
    SYSTEM_ERROR("系统错误")
}

// ==================== 设备操作模型 ====================

/**
 * 设备操作结果
 */
data class DeviceOperationResult(
    val deviceAddress: String,
    val operation: DeviceOperation,
    val result: OperationResult<DeviceStatus>,
    val previousStatus: DeviceStatus? = null,
    val newStatus: DeviceStatus? = null
)

/**
 * 设备操作类型
 */
enum class DeviceOperation(val displayName: String) {
    CONNECT("连接"),
    DISCONNECT("断开"),
    TURN_ON("开启"),
    TURN_OFF("关闭"),
    QUERY_STATUS("查询状态"),
    SET_BRIGHTNESS("设置亮度"),
    SET_SPEED("设置速度")
}

// ==================== 扫描相关模型 ====================

/**
 * 扫描结果
 */
data class ScanResult(
    @SerializedName("devices") val devices: List<RemoteBleDeviceInfo>,
    @SerializedName("scanStatus") val scanStatus: String,
    @SerializedName("totalFound") val totalFound: Int,
    @SerializedName("scanId") val scanId: String? = null
)

/**
 * 扫描状态
 */
enum class ScanStatus(val displayName: String) {
    IDLE("空闲"),
    SCANNING("扫描中"),
    COMPLETED("扫描完成"),
    ERROR("扫描错误")
}

// ==================== 事件数据模型 ====================

/**
 * 蓝牙事件基类
 */
sealed class BluetoothEvent(
    val eventId: String,
    val timestamp: Long,
    val source: String
) {
    data class DeviceFound(
        val device: RemoteBleDeviceInfo,
        val scanId: String
    ) : BluetoothEvent("device_found_${device.address}", System.currentTimeMillis(), "scanner")

    data class DeviceConnected(
        val device: RemoteBleDeviceInfo
    ) : BluetoothEvent("device_connected_${device.address}", System.currentTimeMillis(), "connection_manager")

    data class DeviceDisconnected(
        val deviceAddress: String,
        val reason: String
    ) : BluetoothEvent("device_disconnected_$deviceAddress", System.currentTimeMillis(), "connection_manager")

    data class DeviceStatusChanged(
        val deviceAddress: String,
        val oldStatus: DeviceStatus?,
        val newStatus: DeviceStatus
    ) : BluetoothEvent("status_changed_$deviceAddress", System.currentTimeMillis(), "device_monitor")

    data class ScanStateChanged(
        val isScanning: Boolean,
        val scanId: String?
    ) : BluetoothEvent("scan_state_changed", System.currentTimeMillis(), "scanner")
}

/**
 * 系统事件基类
 */
sealed class SystemEvent(
    val eventId: String,
    val timestamp: Long
) {
    data class ConnectionStateChanged(
        val connectionId: String,
        val oldState: ConnectionState,
        val newState: ConnectionState
    ) : SystemEvent("connection_state_$connectionId", System.currentTimeMillis())

    data class SystemStatusUpdated(
        val systemInfo: SystemInfo
    ) : SystemEvent("system_status", System.currentTimeMillis())

    data class ErrorOccurred(
        val error: OperationError,
        val context: String
    ) : SystemEvent("error_${error.code}", System.currentTimeMillis())
}
