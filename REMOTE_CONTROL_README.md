# 远程蓝牙控制功能说明

## 概述

本次更新为展车控制应用添加了远程蓝牙控制功能，允许手机通过网络连接到树莓派，远程控制树莓派的蓝牙功能。这实现了无屏幕树莓派的远程管理需求。

## 新增功能

### 1. 双模式控制
- **本地控制模式**：直接使用手机蓝牙功能，适用于手机直接连接BLE设备
- **远程控制模式**：通过网络连接树莓派，远程控制树莓派的蓝牙功能

### 2. 树莓派连接管理
- 支持通过IP地址和端口连接树莓派
- 实时显示连接状态和系统信息
- 显示CPU使用率、内存使用率、温度等系统指标
- 显示蓝牙适配器状态和已连接设备数量

### 3. 远程蓝牙设备控制
- 远程启动/停止蓝牙扫描
- 查看扫描到的BLE设备列表
- 远程连接/断开BLE设备
- 远程控制设备开关状态
- 实时同步设备状态变化

### 4. 实时通信
- 基于WebSocket的实时事件通信
- 设备发现、连接状态变化、系统状态更新等实时推送
- 心跳机制确保连接稳定性

## 技术架构

### 三层架构
```
手机应用 ↔ 树莓派 ↔ BLE设备
```

### 通信协议
- **HTTP/HTTPS REST API**：用于基本的CRUD操作
- **WebSocket**：用于实时事件推送和双向通信

### 核心组件

#### 1. 数据模型 (RemoteDeviceModels.kt)
- `RemoteBleDeviceInfo`：远程BLE设备信息
- `RaspberryPiConnection`：树莓派连接信息
- `SystemInfo`：系统状态信息
- `BluetoothEvent`：蓝牙事件模型
- `SystemEvent`：系统事件模型

#### 2. 网络层
- `NetworkManager`：网络连接管理器
- `RaspberryPiApiService`：REST API服务接口
- `WebSocketManager`：WebSocket通信管理器

#### 3. 数据层
- `RemoteBluetoothRepository`：远程蓝牙数据仓库，统一管理API调用和WebSocket通信

#### 4. 业务层
- `RemoteBluetoothViewModel`：远程蓝牙控制ViewModel，管理UI状态和业务逻辑

#### 5. UI层
- `MainScreen`：主界面，支持本地/远程模式切换
- `RaspberryPiConnectionSection`：树莓派连接管理界面
- `RemoteDeviceControlSection`：远程设备控制界面

## 使用说明

### 1. 启动应用
1. 打开展车控制应用
2. 在主界面选择"远程控制"模式

### 2. 连接树莓派
1. 点击"连接树莓派"按钮
2. 输入树莓派的IP地址（如：*************）
3. 输入端口号（默认：8080）
4. 点击"连接"按钮

### 3. 远程蓝牙控制
1. 连接成功后，可以看到树莓派的系统状态
2. 点击"开始扫描"开始蓝牙设备扫描
3. 在"已发现设备"列表中点击"连接"按钮连接设备
4. 在"已连接设备"列表中使用开关控制设备状态
5. 点击"断开"按钮可以断开设备连接

### 4. 实时状态监控
- 系统状态会实时更新显示
- 设备连接状态变化会实时同步
- 扫描到的新设备会实时显示在列表中

## API接口说明

### REST API端点
- `GET /api/system/status` - 获取系统状态
- `POST /api/bluetooth/scan/start` - 开始蓝牙扫描
- `POST /api/bluetooth/scan/stop` - 停止蓝牙扫描
- `GET /api/bluetooth/scan/results` - 获取扫描结果
- `GET /api/devices/connected` - 获取已连接设备
- `POST /api/devices/{address}/connect` - 连接设备
- `DELETE /api/devices/{address}/disconnect` - 断开设备
- `POST /api/devices/{address}/control` - 控制设备

### WebSocket事件
- `device_found` - 设备发现事件
- `device_connected` - 设备连接事件
- `device_disconnected` - 设备断开事件
- `device_status_changed` - 设备状态变化事件
- `scan_state_changed` - 扫描状态变化事件
- `system_status_updated` - 系统状态更新事件

## 支持的设备类型

- **无线充电器** (代码: 02) 🔌
- **主灯** (代码: 03) 💡
- **氛围灯** (代码: 04) 🌈
- **香薰机** (代码: 05) 🌸
- **风扇** (代码: 06) 🌀
- **按键设备** (代码: 99) 🔘

## 错误处理

应用包含完善的错误处理机制：
- 网络连接错误
- 树莓派连接失败
- 蓝牙操作失败
- 设备控制错误
- WebSocket连接中断

所有错误都会在UI中显示详细的错误信息，帮助用户快速定位和解决问题。

## 版本信息

- **版本号**：2.0
- **版本代码**：2
- **更新内容**：添加远程蓝牙控制功能

## 依赖库更新

新增以下依赖库：
- Retrofit2：HTTP客户端
- OkHttp3：网络通信
- Gson：JSON序列化
- WebSocket：实时通信
- ViewModel：状态管理
- Coroutines：异步处理

## 测试

包含完整的单元测试：
- 数据模型测试
- 仓库模式测试
- 事件系统测试
- 连接状态测试

运行测试：
```bash
./gradlew test
```

## 编译和运行

### 编译项目
```bash
# Windows
gradlew.bat clean assembleDebug

# Linux/Mac
./gradlew clean assembleDebug
```

### 测试编译
运行 `test-build.bat` 脚本进行快速编译测试。

### 已知问题和解决方案

1. **Material Icons问题**：已替换不兼容的图标为通用图标
2. **BuildConfig问题**：已临时禁用BuildConfig.DEBUG检查
3. **版本兼容性**：已更新到版本2.0，包含所有新功能

## 注意事项

1. 确保手机和树莓派在同一网络中
2. 树莓派需要运行对应的API服务
3. 确保树莓派的防火墙允许相应端口访问
4. 建议使用稳定的WiFi网络以确保通信质量
5. 当前版本为手机端实现，树莓派端API需要单独开发

## 后续开发计划

1. **树莓派端API服务开发** - 实现HTTP REST API和WebSocket服务
2. **通信协议完整实现** - 完善请求/响应处理和错误处理
3. **设备控制功能优化** - 改进设备连接稳定性和控制响应速度
4. **性能测试和优化** - 网络通信优化和内存使用优化
5. **用户体验改进** - UI/UX优化和错误提示改进
6. **单元测试完善** - 增加更多测试用例确保代码质量

## 开发状态

✅ **已完成**：
- 手机端架构设计和实现
- 网络通信层（HTTP + WebSocket）
- 数据模型和状态管理
- UI界面和用户交互
- 基础单元测试

🔄 **进行中**：
- 编译错误修复和优化

⏳ **待开发**：
- 树莓派端API服务
- 端到端集成测试
- 性能优化
