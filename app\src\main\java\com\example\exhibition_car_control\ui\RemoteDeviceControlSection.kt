package com.example.exhibition_car_control.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.exhibition_car_control.model.*
import com.example.exhibition_car_control.viewmodel.RemoteBluetoothUiState

/**
 * 远程设备控制界面
 */
@Composable
fun RemoteDeviceControlSection(
    uiState: RemoteBluetoothUiState,
    connectedDevices: List<RemoteBleDeviceInfo>,
    discoveredDevices: List<RemoteBleDeviceInfo>,
    scanStatus: ScanStatus,
    onStartScan: () -> Unit,
    onStopScan: () -> Unit,
    onConnectDevice: (String) -> Unit,
    onDisconnectDevice: (String) -> Unit,
    onToggleDevice: (String, DeviceType, Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 扫描控制区域
        ScanControlCard(
            scanStatus = scanStatus,
            scanError = uiState.scanError,
            onStartScan = onStartScan,
            onStopScan = onStopScan
        )

        // 已连接设备列表
        ConnectedDevicesCard(
            devices = connectedDevices,
            controllingDevices = uiState.controllingDevices,
            onDisconnectDevice = onDisconnectDevice,
            onToggleDevice = onToggleDevice
        )

        // 已发现设备列表
        if (discoveredDevices.isNotEmpty()) {
            DiscoveredDevicesCard(
                devices = discoveredDevices,
                connectingDevices = uiState.connectingDevices,
                onConnectDevice = onConnectDevice
            )
        }
    }
}

/**
 * 扫描控制卡片
 */
@Composable
private fun ScanControlCard(
    scanStatus: ScanStatus,
    scanError: String?,
    onStartScan: () -> Unit,
    onStopScan: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "🔍 蓝牙扫描控制",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
                
                ScanStatusIndicator(scanStatus)
            }

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Button(
                    onClick = onStartScan,
                    enabled = scanStatus != ScanStatus.SCANNING,
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(
                        imageVector = Icons.Default.Search,
                        contentDescription = "开始扫描",
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("开始扫描")
                }
                
                Button(
                    onClick = onStopScan,
                    enabled = scanStatus == ScanStatus.SCANNING,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFFF44336)
                    ),
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(
                        imageVector = Icons.Default.Stop,
                        contentDescription = "停止扫描",
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("停止扫描")
                }
            }

            // 扫描错误显示
            scanError?.let { error ->
                Text(
                    text = error,
                    fontSize = 12.sp,
                    color = Color(0xFFF44336)
                )
            }
        }
    }
}

/**
 * 扫描状态指示器
 */
@Composable
private fun ScanStatusIndicator(scanStatus: ScanStatus) {
    val (color, icon, text) = when (scanStatus) {
        ScanStatus.IDLE -> Triple(Color(0xFF9E9E9E), Icons.Default.RadioButtonUnchecked, "空闲")
        ScanStatus.SCANNING -> Triple(Color(0xFF2196F3), Icons.Default.Search, "扫描中")
        ScanStatus.COMPLETED -> Triple(Color(0xFF4CAF50), Icons.Default.CheckCircle, "完成")
        ScanStatus.ERROR -> Triple(Color(0xFFF44336), Icons.Default.Error, "错误")
    }

    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        if (scanStatus == ScanStatus.SCANNING) {
            CircularProgressIndicator(
                modifier = Modifier.size(16.dp),
                strokeWidth = 2.dp,
                color = color
            )
        } else {
            Icon(
                imageVector = icon,
                contentDescription = text,
                tint = color,
                modifier = Modifier.size(16.dp)
            )
        }
        Text(
            text = text,
            fontSize = 12.sp,
            color = color,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 已连接设备卡片
 */
@Composable
private fun ConnectedDevicesCard(
    devices: List<RemoteBleDeviceInfo>,
    controllingDevices: Set<String>,
    onDisconnectDevice: (String) -> Unit,
    onToggleDevice: (String, DeviceType, Boolean) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "📱 已连接设备 (${devices.size})",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )

            if (devices.isEmpty()) {
                EmptyDeviceList("暂无已连接设备")
            } else {
                LazyColumn(
                    modifier = Modifier.heightIn(max = 300.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(devices) { device ->
                        ConnectedDeviceItem(
                            device = device,
                            isControlling = controllingDevices.contains(device.address),
                            onDisconnect = { onDisconnectDevice(device.address) },
                            onToggle = { isOn ->
                                val deviceType = DeviceType.fromCode(device.deviceType)
                                onToggleDevice(device.address, deviceType, isOn)
                            }
                        )
                    }
                }
            }
        }
    }
}

/**
 * 已连接设备项
 */
@Composable
private fun ConnectedDeviceItem(
    device: RemoteBleDeviceInfo,
    isControlling: Boolean,
    onDisconnect: () -> Unit,
    onToggle: (Boolean) -> Unit
) {
    val deviceType = DeviceType.fromCode(device.deviceType)
    val isOn = device.status?.isOn ?: false

    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 设备信息
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = deviceType.icon,
                    fontSize = 20.sp
                )
                
                Column {
                    Text(
                        text = device.name ?: deviceType.displayName,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = device.address,
                        fontSize = 10.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            // 控制按钮
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 开关控制
                if (deviceType != DeviceType.BUTTON_DEVICE) {
                    Switch(
                        checked = isOn,
                        onCheckedChange = onToggle,
                        enabled = !isControlling,
                        colors = SwitchDefaults.colors(
                            checkedThumbColor = Color(0xFF4CAF50),
                            checkedTrackColor = Color(0xFF4CAF50).copy(alpha = 0.5f)
                        )
                    )
                    
                    if (isControlling) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            strokeWidth = 2.dp
                        )
                    }
                }

                // 断开按钮
                IconButton(
                    onClick = onDisconnect,
                    modifier = Modifier.size(32.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "断开连接",
                        tint = Color(0xFFF44336),
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
}

/**
 * 已发现设备卡片
 */
@Composable
private fun DiscoveredDevicesCard(
    devices: List<RemoteBleDeviceInfo>,
    connectingDevices: Set<String>,
    onConnectDevice: (String) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "🔍 已发现设备 (${devices.size})",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )

            LazyColumn(
                modifier = Modifier.heightIn(max = 200.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(devices) { device ->
                    DiscoveredDeviceItem(
                        device = device,
                        isConnecting = connectingDevices.contains(device.address),
                        onConnect = { onConnectDevice(device.address) }
                    )
                }
            }
        }
    }
}

/**
 * 已发现设备项
 */
@Composable
private fun DiscoveredDeviceItem(
    device: RemoteBleDeviceInfo,
    isConnecting: Boolean,
    onConnect: () -> Unit
) {
    val deviceType = DeviceType.fromCode(device.deviceType)

    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 设备信息
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = deviceType.icon,
                    fontSize = 20.sp
                )
                
                Column {
                    Text(
                        text = device.name ?: deviceType.displayName,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium
                    )
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Text(
                            text = device.address,
                            fontSize = 10.sp,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Text(
                            text = "${device.rssi}dBm",
                            fontSize = 10.sp,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }

            // 连接按钮
            Button(
                onClick = onConnect,
                enabled = !isConnecting,
                modifier = Modifier.height(32.dp)
            ) {
                if (isConnecting) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                }
                Text(
                    text = if (isConnecting) "连接中" else "连接",
                    fontSize = 12.sp
                )
            }
        }
    }
}

/**
 * 空设备列表显示
 */
@Composable
private fun EmptyDeviceList(message: String) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Icon(
            imageVector = Icons.Default.DevicesOther,
            contentDescription = message,
            tint = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.size(48.dp)
        )
        Text(
            text = message,
            fontSize = 14.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}
