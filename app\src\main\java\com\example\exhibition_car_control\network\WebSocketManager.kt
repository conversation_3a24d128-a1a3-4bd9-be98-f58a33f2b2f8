package com.example.exhibition_car_control.network

import android.util.Log
import com.example.exhibition_car_control.model.*
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import okhttp3.*
import java.util.concurrent.TimeUnit

/**
 * WebSocket管理器，处理与树莓派的实时通信
 */
class WebSocketManager {
    
    companion object {
        private const val TAG = "WebSocketManager"
        private const val HEARTBEAT_INTERVAL = 30_000L // 30秒心跳间隔
    }

    private var webSocket: WebSocket? = null
    private val gson = Gson()
    private var isConnected = false
    private var raspberryPiAddress: String? = null

    // 事件流
    private val _bluetoothEvents = MutableSharedFlow<BluetoothEvent>()
    val bluetoothEvents: SharedFlow<BluetoothEvent> = _bluetoothEvents.asSharedFlow()

    private val _systemEvents = MutableSharedFlow<SystemEvent>()
    val systemEvents: SharedFlow<SystemEvent> = _systemEvents.asSharedFlow()

    private val _connectionState = MutableSharedFlow<ConnectionState>()
    val connectionState: SharedFlow<ConnectionState> = _connectionState.asSharedFlow()

    private val _errorEvents = MutableSharedFlow<String>()
    val errorEvents: SharedFlow<String> = _errorEvents.asSharedFlow()

    /**
     * 连接到树莓派WebSocket服务
     */
    fun connect(address: String, port: Int = 8080) {
        disconnect() // 先断开现有连接

        raspberryPiAddress = address
        val url = "ws://$address:$port/ws/bluetooth/events"
        
        Log.d(TAG, "连接到WebSocket: $url")

        val client = OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(0, TimeUnit.SECONDS) // WebSocket需要长连接
            .writeTimeout(10, TimeUnit.SECONDS)
            .build()

        val request = Request.Builder()
            .url(url)
            .build()

        webSocket = client.newWebSocket(request, object : WebSocketListener() {
            override fun onOpen(webSocket: WebSocket, response: Response) {
                Log.d(TAG, "WebSocket连接已建立")
                isConnected = true
                _connectionState.tryEmit(ConnectionState.CONNECTED)
                
                // 发送认证消息
                sendAuthMessage()
            }

            override fun onMessage(webSocket: WebSocket, text: String) {
                Log.d(TAG, "收到WebSocket消息: $text")
                handleMessage(text)
            }

            override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
                Log.d(TAG, "WebSocket连接正在关闭: $code - $reason")
                _connectionState.tryEmit(ConnectionState.DISCONNECTING)
            }

            override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
                Log.d(TAG, "WebSocket连接已关闭: $code - $reason")
                isConnected = false
                _connectionState.tryEmit(ConnectionState.DISCONNECTED)
            }

            override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                Log.e(TAG, "WebSocket连接失败", t)
                isConnected = false
                _connectionState.tryEmit(ConnectionState.ERROR)
                _errorEvents.tryEmit("WebSocket连接失败: ${t.message}")
            }
        })
    }

    /**
     * 断开WebSocket连接
     */
    fun disconnect() {
        webSocket?.close(1000, "客户端主动断开")
        webSocket = null
        isConnected = false
        _connectionState.tryEmit(ConnectionState.DISCONNECTED)
    }

    /**
     * 发送认证消息
     */
    private fun sendAuthMessage() {
        val authMessage = mapOf(
            "type" to "auth",
            "token" to "mobile_app_token",
            "clientId" to "mobile_${System.currentTimeMillis()}"
        )
        sendMessage(authMessage)
    }

    /**
     * 发送消息
     */
    private fun sendMessage(message: Any) {
        if (!isConnected) {
            Log.w(TAG, "WebSocket未连接，无法发送消息")
            return
        }

        try {
            val json = gson.toJson(message)
            webSocket?.send(json)
            Log.d(TAG, "发送WebSocket消息: $json")
        } catch (e: Exception) {
            Log.e(TAG, "发送WebSocket消息失败", e)
            _errorEvents.tryEmit("发送消息失败: ${e.message}")
        }
    }

    /**
     * 发送设备控制指令
     */
    fun sendDeviceControl(deviceAddress: String, deviceType: String, command: String, data: String = "") {
        val controlMessage = mapOf(
            "commandId" to "cmd_${System.currentTimeMillis()}",
            "timestamp" to System.currentTimeMillis(),
            "type" to "device_control",
            "target" to deviceAddress,
            "action" to mapOf(
                "deviceType" to deviceType,
                "command" to command,
                "data" to data,
                "timeout" to 5000
            )
        )
        sendMessage(controlMessage)
    }

    /**
     * 发送心跳消息
     */
    fun sendHeartbeat() {
        val heartbeatMessage = mapOf(
            "type" to "heartbeat",
            "timestamp" to System.currentTimeMillis(),
            "clientStatus" to "active",
            "requestSync" to false
        )
        sendMessage(heartbeatMessage)
    }

    /**
     * 请求状态同步
     */
    fun requestSync(scope: List<String> = listOf("devices", "connections", "system")) {
        val syncMessage = mapOf(
            "type" to "sync_request",
            "scope" to scope,
            "lastSyncTime" to System.currentTimeMillis()
        )
        sendMessage(syncMessage)
    }

    /**
     * 处理收到的消息
     */
    private fun handleMessage(text: String) {
        try {
            val message = gson.fromJson(text, Map::class.java)
            val type = message["type"] as? String ?: return

            when (type) {
                "device_found" -> handleDeviceFoundEvent(message)
                "device_connected" -> handleDeviceConnectedEvent(message)
                "device_disconnected" -> handleDeviceDisconnectedEvent(message)
                "device_status_changed" -> handleDeviceStatusChangedEvent(message)
                "scan_state_changed" -> handleScanStateChangedEvent(message)
                "system_status" -> handleSystemStatusEvent(message)
                "heartbeat_ack" -> handleHeartbeatAck(message)
                "sync_response" -> handleSyncResponse(message)
                "error" -> handleErrorEvent(message)
                else -> Log.w(TAG, "未知消息类型: $type")
            }
        } catch (e: JsonSyntaxException) {
            Log.e(TAG, "解析WebSocket消息失败", e)
            _errorEvents.tryEmit("消息解析失败: ${e.message}")
        } catch (e: Exception) {
            Log.e(TAG, "处理WebSocket消息失败", e)
            _errorEvents.tryEmit("消息处理失败: ${e.message}")
        }
    }

    /**
     * 处理设备发现事件
     */
    private fun handleDeviceFoundEvent(message: Map<*, *>) {
        try {
            val data = message["data"] as? Map<*, *> ?: return
            val deviceData = data["device"] as? Map<*, *> ?: return
            val scanId = data["scanId"] as? String ?: ""

            val device = parseDeviceInfo(deviceData)
            val event = BluetoothEvent.DeviceFound(device, scanId)
            _bluetoothEvents.tryEmit(event)
        } catch (e: Exception) {
            Log.e(TAG, "处理设备发现事件失败", e)
        }
    }

    /**
     * 处理设备连接事件
     */
    private fun handleDeviceConnectedEvent(message: Map<*, *>) {
        try {
            val data = message["data"] as? Map<*, *> ?: return
            val deviceData = data["device"] as? Map<*, *> ?: return

            val device = parseDeviceInfo(deviceData)
            val event = BluetoothEvent.DeviceConnected(device)
            _bluetoothEvents.tryEmit(event)
        } catch (e: Exception) {
            Log.e(TAG, "处理设备连接事件失败", e)
        }
    }

    /**
     * 处理设备断开事件
     */
    private fun handleDeviceDisconnectedEvent(message: Map<*, *>) {
        try {
            val data = message["data"] as? Map<*, *> ?: return
            val deviceAddress = data["deviceAddress"] as? String ?: return
            val reason = data["reason"] as? String ?: "未知原因"

            val event = BluetoothEvent.DeviceDisconnected(deviceAddress, reason)
            _bluetoothEvents.tryEmit(event)
        } catch (e: Exception) {
            Log.e(TAG, "处理设备断开事件失败", e)
        }
    }

    /**
     * 处理设备状态变化事件
     */
    private fun handleDeviceStatusChangedEvent(message: Map<*, *>) {
        try {
            val data = message["data"] as? Map<*, *> ?: return
            val deviceAddress = data["deviceAddress"] as? String ?: return
            val oldStatusData = data["oldStatus"] as? Map<*, *>
            val newStatusData = data["newStatus"] as? Map<*, *> ?: return

            val oldStatus = oldStatusData?.let { parseDeviceStatus(it) }
            val newStatus = parseDeviceStatus(newStatusData)

            val event = BluetoothEvent.DeviceStatusChanged(deviceAddress, oldStatus, newStatus)
            _bluetoothEvents.tryEmit(event)
        } catch (e: Exception) {
            Log.e(TAG, "处理设备状态变化事件失败", e)
        }
    }

    /**
     * 处理扫描状态变化事件
     */
    private fun handleScanStateChangedEvent(message: Map<*, *>) {
        try {
            val data = message["data"] as? Map<*, *> ?: return
            val isScanning = data["isScanning"] as? Boolean ?: false
            val scanId = data["scanId"] as? String

            val event = BluetoothEvent.ScanStateChanged(isScanning, scanId)
            _bluetoothEvents.tryEmit(event)
        } catch (e: Exception) {
            Log.e(TAG, "处理扫描状态变化事件失败", e)
        }
    }

    /**
     * 处理系统状态事件
     */
    private fun handleSystemStatusEvent(message: Map<*, *>) {
        try {
            val data = message["data"] as? Map<*, *> ?: return
            val systemInfoJson = gson.toJson(data)
            val systemInfo = gson.fromJson(systemInfoJson, SystemInfo::class.java)

            val event = SystemEvent.SystemStatusUpdated(systemInfo)
            _systemEvents.tryEmit(event)
        } catch (e: Exception) {
            Log.e(TAG, "处理系统状态事件失败", e)
        }
    }

    /**
     * 处理心跳确认
     */
    private fun handleHeartbeatAck(message: Map<*, *>) {
        Log.d(TAG, "收到心跳确认")
        // 可以在这里更新连接质量信息
    }

    /**
     * 处理同步响应
     */
    private fun handleSyncResponse(message: Map<*, *>) {
        Log.d(TAG, "收到同步响应")
        // 处理同步数据
    }

    /**
     * 处理错误事件
     */
    private fun handleErrorEvent(message: Map<*, *>) {
        val errorMessage = message["message"] as? String ?: "未知错误"
        _errorEvents.tryEmit(errorMessage)
    }

    /**
     * 解析设备信息
     */
    private fun parseDeviceInfo(deviceData: Map<*, *>): RemoteBleDeviceInfo {
        return RemoteBleDeviceInfo(
            address = deviceData["address"] as? String ?: "",
            name = deviceData["name"] as? String,
            rssi = (deviceData["rssi"] as? Number)?.toInt() ?: 0,
            deviceType = deviceData["deviceType"] as? String ?: "FF",
            connectionState = deviceData["connectionState"] as? String ?: "DISCONNECTED",
            lastSeen = (deviceData["lastSeen"] as? Number)?.toLong() ?: System.currentTimeMillis(),
            services = (deviceData["services"] as? List<*>)?.mapNotNull { it as? String } ?: emptyList(),
            batteryLevel = (deviceData["batteryLevel"] as? Number)?.toInt(),
            firmwareVersion = deviceData["firmwareVersion"] as? String,
            isOnline = deviceData["isOnline"] as? Boolean ?: false,
            lastStatusUpdate = (deviceData["lastStatusUpdate"] as? Number)?.toLong() ?: 0L,
            status = (deviceData["status"] as? Map<*, *>)?.let { parseDeviceStatus(it) }
        )
    }

    /**
     * 解析设备状态
     */
    private fun parseDeviceStatus(statusData: Map<*, *>): DeviceStatus {
        return DeviceStatus(
            deviceType = statusData["deviceType"] as? String ?: "FF",
            isOn = statusData["isOn"] as? Boolean ?: false,
            brightness = (statusData["brightness"] as? Number)?.toInt(),
            speed = (statusData["speed"] as? Number)?.toInt(),
            batteryLevel = (statusData["batteryLevel"] as? Number)?.toInt(),
            temperature = (statusData["temperature"] as? Number)?.toFloat(),
            errorCode = statusData["errorCode"] as? String,
            lastUpdate = (statusData["lastUpdate"] as? Number)?.toLong() ?: System.currentTimeMillis(),
            rawData = (statusData["rawData"] as? List<*>)?.mapNotNull { (it as? Number)?.toInt() }
        )
    }

    /**
     * 检查连接状态
     */
    fun isConnected(): Boolean = isConnected

    /**
     * 获取当前连接的树莓派地址
     */
    fun getCurrentAddress(): String? = raspberryPiAddress
}
