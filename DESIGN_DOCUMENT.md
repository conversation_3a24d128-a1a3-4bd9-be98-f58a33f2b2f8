# 📋 ZeroSense 蓝牙远程控制系统设计文档

## 📖 文档信息
- **版本**: v1.0
- **创建日期**: 2025-01-01
- **最后更新**: 2025-01-01
- **作者**: ZeroSense Team

## 🎯 项目概述

### 背景
树莓派在实际使用中通常没有屏幕，需要通过手机远程控制其蓝牙功能，包括扫描、连接、管理BLE设备等操作。

### 目标
设计并实现一个完整的远程蓝牙控制系统，让手机端可以：
- 连接和管理树莓派
- 远程控制树莓派的蓝牙扫描
- 查看和管理已连接的BLE设备
- 远程控制BLE设备的开关状态
- 实时监控设备状态和系统状态

## 🏗️ 系统架构

### 整体架构图
```
┌─────────────────┐    HTTP/WebSocket    ┌─────────────────┐    BLE协议    ┌─────────────────┐
│                 │ ←─────────────────→ │                 │ ←──────────→ │                 │
│   📱 手机端      │                     │  🖥️ 树莓派端     │              │  📱 BLE设备      │
│                 │                     │                 │              │                 │
│ - 远程控制界面   │                     │ - HTTP API服务   │              │ - 充电器        │
│ - 设备状态显示   │                     │ - WebSocket服务  │              │ - 灯具          │
│ - 连接管理      │                     │ - 蓝牙管理服务   │              │ - 香氛机        │
│ - 实时监控      │                     │ - 设备控制服务   │              │ - 风扇          │
└─────────────────┘                     └─────────────────┘              └─────────────────┘
```

### 技术栈
**手机端**:
- Android + Kotlin + Jetpack Compose
- Retrofit + OkHttp (HTTP通信)
- OkHttp WebSocket (实时通信)
- Room数据库 (本地存储)

**树莓派端**:
- 现有蓝牙SDK基础
- Spring Boot + Kotlin (HTTP API)
- WebSocket服务 (实时推送)
- SQLite数据库 (状态存储)

## 📱 手机端功能设计

### 核心功能模块

#### 1. 树莓派连接管理
```
🔍 设备发现
├── 自动扫描局域网内的树莓派
├── 手动输入IP地址连接
├── 历史连接记录
└── 连接状态监控

🔗 连接管理
├── 建立HTTP/WebSocket连接
├── 连接状态实时显示
├── 自动重连机制
└── 连接质量监控
```

#### 2. 远程蓝牙控制
```
🎮 扫描控制
├── 开启/停止蓝牙扫描
├── 扫描参数设置
├── 实时扫描结果显示
└── 设备过滤功能

🔗 连接控制
├── 连接指定BLE设备
├── 断开设备连接
├── 批量连接操作
└── 连接优先级管理
```

#### 3. 设备状态管理
```
📊 状态显示
├── 已连接设备列表
├── 设备类型图标显示
├── 实时状态更新
└── 设备详细信息

🎛️ 设备控制
├── 开关控制
├── 状态查询
├── 批量操作
└── 操作历史记录
```

### UI界面设计

#### 主界面布局
```
┌─────────────────────────────────┐
│ 🏠 ZeroSense 远程控制            │
├─────────────────────────────────┤
│ 📡 树莓派连接状态                │
│ ┌─────────────────────────────┐ │
│ │ 🟢 已连接: RaspberryPi-001  │ │
│ │ 📶 信号: -45dBm 延迟: 12ms  │ │
│ └─────────────────────────────┘ │
├─────────────────────────────────┤
│ 🔍 蓝牙扫描控制                 │
│ [开始扫描] [停止扫描] [设置]     │
├─────────────────────────────────┤
│ 📱 已连接设备 (3)               │
│ ┌─────────────────────────────┐ │
│ │ 💡 主灯        🟢 开启      │ │
│ │ 🔌 无线充电器   🔴 关闭      │ │
│ │ 🌸 香氛机      🟢 开启      │ │
│ └─────────────────────────────┘ │
└─────────────────────────────────┘
```

## 🖥️ 树莓派端API设计

### HTTP API接口

#### 1. 系统状态接口
```http
GET /api/system/status
Response: {
  "cpu": 45.2,
  "memory": 67.8,
  "temperature": 42.5,
  "bluetooth": {
    "enabled": true,
    "scanning": false,
    "connectedDevices": 3
  }
}
```

#### 2. 蓝牙扫描接口
```http
POST /api/bluetooth/scan/start
Request: {
  "duration": 30,
  "deviceTypes": ["BLE"],
  "filters": {"rssiThreshold": -80}
}
Response: {
  "success": true,
  "scanId": "scan_123"
}

GET /api/bluetooth/scan/results
Response: {
  "devices": [
    {
      "address": "AA:BB:CC:DD:EE:FF",
      "name": "无线充电器",
      "rssi": -65,
      "deviceType": "02"
    }
  ]
}
```

#### 3. 设备控制接口
```http
POST /api/devices/{address}/control
Request: {
  "deviceType": "03",
  "command": "01"
}
Response: {
  "success": true,
  "status": "01",
  "message": "主灯已开启"
}

GET /api/devices/connected
Response: {
  "devices": [
    {
      "address": "AA:BB:CC:DD:EE:FF",
      "name": "主灯",
      "type": "03",
      "status": "01",
      "lastUpdate": "2025-01-01T12:00:00Z"
    }
  ]
}
```

### WebSocket事件

#### 实时事件推送
```javascript
// 设备发现事件
{
  "type": "device_found",
  "data": {
    "address": "AA:BB:CC:DD:EE:FF",
    "name": "新设备",
    "rssi": -70
  }
}

// 设备状态变化
{
  "type": "device_status_changed",
  "data": {
    "address": "AA:BB:CC:DD:EE:FF",
    "oldStatus": "00",
    "newStatus": "01"
  }
}

// 系统状态更新
{
  "type": "system_status",
  "data": {
    "cpu": 50.1,
    "connectedDevices": 4
  }
}
```

## 📊 数据模型设计

### 核心数据结构

#### BLE设备信息
```kotlin
data class BleDeviceInfo(
    val address: String,           // MAC地址
    val name: String?,             // 设备名称
    val rssi: Int,                // 信号强度
    val deviceType: DeviceType,    // 设备类型
    val connectionState: ConnectionState, // 连接状态
    val lastSeen: Long,           // 最后发现时间
    val status: DeviceStatus?     // 设备状态
)

enum class DeviceType(val code: String, val displayName: String) {
    WIRELESS_CHARGER("02", "无线充电器"),
    LAMP_MAIN("03", "主灯"),
    LAMP_AMBIENT("04", "氛围灯"),
    FRAGRANCE_MACHINE("05", "香氛机"),
    FAN("06", "风扇")
}

data class DeviceStatus(
    val isOn: Boolean,            // 是否开启
    val brightness: Int?,         // 亮度(0-100)
    val speed: Int?,             // 速度(0-100)
    val batteryLevel: Int?,      // 电池电量
    val lastUpdate: Long         // 最后更新时间
)
```

#### 树莓派连接信息
```kotlin
data class RaspberryPiConnection(
    val id: String,               // 连接ID
    val address: String,          // IP地址
    val name: String,            // 设备名称
    val state: ConnectionState,   // 连接状态
    val connectedAt: Long?,      // 连接时间
    val lastHeartbeat: Long?,    // 最后心跳
    val latency: Long?,          // 延迟(ms)
    val systemInfo: SystemInfo?  // 系统信息
)
```

## 🔄 通信协议设计

### 协议层次
```
应用层: JSON格式的业务数据
传输层: HTTP/HTTPS + WebSocket
网络层: TCP/IP
物理层: WiFi/以太网
```

### 消息格式

#### HTTP请求格式
```json
{
  "requestId": "req_12345",
  "timestamp": "2025-01-01T12:00:00Z",
  "command": "scan_start",
  "parameters": {
    "duration": 30,
    "filters": {...}
  }
}
```

#### 统一响应格式
```json
{
  "requestId": "req_12345",
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {...},
  "timestamp": "2025-01-01T12:00:01Z"
}
```

### 错误处理

#### 错误码定义
```
1000-1999: 系统错误
2000-2999: 网络错误  
3000-3999: 蓝牙错误
4000-4999: 设备错误
5000-5999: 权限错误
```

#### 重试机制
- 网络错误: 指数退避重试，最多3次
- 设备错误: 立即重试1次，失败则报错
- 系统错误: 不重试，直接报错

## 🚀 实施计划

### 开发阶段

#### 第一阶段: 基础框架 (1-2周)
- [ ] 创建HTTP API基础框架
- [ ] 实现WebSocket连接
- [ ] 完成基本的设备发现功能
- [ ] 手机端连接管理界面

#### 第二阶段: 核心功能 (2-3周)  
- [ ] 远程蓝牙扫描控制
- [ ] 设备连接管理
- [ ] 基础设备控制功能
- [ ] 状态同步机制

#### 第三阶段: 高级功能 (1-2周)
- [ ] 实时状态监控
- [ ] 批量操作功能
- [ ] 错误处理和恢复
- [ ] 性能优化

#### 第四阶段: 测试完善 (1周)
- [ ] 功能测试
- [ ] 稳定性测试  
- [ ] UI/UX优化
- [ ] 文档完善

### 技术风险评估

#### 高风险
- 网络连接稳定性
- 蓝牙设备兼容性
- 实时性能要求

#### 中风险  
- 多设备并发控制
- 状态同步一致性
- 错误恢复机制

#### 低风险
- UI界面开发
- 基础API实现
- 数据存储

## 📝 总结

本设计文档提供了完整的远程蓝牙控制系统架构，包括手机端和树莓派端的详细设计。通过HTTP API和WebSocket实现可靠的远程通信，支持实时的设备状态监控和控制。

设计重点关注：
- 🔄 实时性: WebSocket确保状态实时同步
- 🛡️ 可靠性: 完善的错误处理和重试机制  
- 🎯 易用性: 直观的UI界面和操作流程
- 🔧 扩展性: 支持新设备类型的轻松添加

下一步将按照实施计划逐步开发各个功能模块。
