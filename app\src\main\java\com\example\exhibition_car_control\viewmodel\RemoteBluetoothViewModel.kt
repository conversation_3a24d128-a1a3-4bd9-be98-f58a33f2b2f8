package com.example.exhibition_car_control.viewmodel

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.exhibition_car_control.model.*
import com.example.exhibition_car_control.repository.RemoteBluetoothRepository
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay

/**
 * 远程蓝牙控制ViewModel
 */
class RemoteBluetoothViewModel : ViewModel() {
    
    companion object {
        private const val TAG = "RemoteBluetoothViewModel"
        private const val HEARTBEAT_INTERVAL = 30_000L // 30秒心跳间隔
    }

    private val repository = RemoteBluetoothRepository.getInstance()

    // UI状态
    private val _uiState = MutableStateFlow(RemoteBluetoothUiState())
    val uiState: StateFlow<RemoteBluetoothUiState> = _uiState.asStateFlow()

    // 仓库状态流
    val connectionState = repository.connectionState
    val connectedDevices = repository.connectedDevices
    val discoveredDevices = repository.discoveredDevices
    val systemInfo = repository.systemInfo
    val scanStatus = repository.scanStatus
    val currentConnection = repository.currentConnection

    // 事件流
    val bluetoothEvents = repository.bluetoothEvents
    val systemEvents = repository.systemEvents
    val errorEvents = repository.errorEvents

    init {
        // 监听连接状态变化
        viewModelScope.launch {
            connectionState.collect { state ->
                _uiState.value = _uiState.value.copy(
                    isConnecting = state == ConnectionState.CONNECTING,
                    isConnected = state == ConnectionState.CONNECTED,
                    connectionError = if (state == ConnectionState.ERROR) "连接失败" else null
                )
                
                // 连接成功后开始心跳
                if (state == ConnectionState.CONNECTED) {
                    startHeartbeat()
                }
            }
        }

        // 监听蓝牙事件
        viewModelScope.launch {
            bluetoothEvents.collect { event ->
                handleBluetoothEvent(event)
            }
        }

        // 监听错误事件
        viewModelScope.launch {
            errorEvents.collect { error ->
                _uiState.value = _uiState.value.copy(
                    lastError = error,
                    showError = true
                )
            }
        }

        // 监听扫描状态
        viewModelScope.launch {
            scanStatus.collect { status ->
                _uiState.value = _uiState.value.copy(
                    isScanning = status == ScanStatus.SCANNING
                )
            }
        }
    }

    /**
     * 连接到树莓派
     */
    fun connectToRaspberryPi(address: String, port: Int = 8080) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(
                    isConnecting = true,
                    connectionError = null
                )
                
                val result = repository.connectToRaspberryPi(address, port)
                
                if (result.isFailure) {
                    _uiState.value = _uiState.value.copy(
                        isConnecting = false,
                        connectionError = result.exceptionOrNull()?.message ?: "连接失败"
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "连接树莓派时发生错误", e)
                _uiState.value = _uiState.value.copy(
                    isConnecting = false,
                    connectionError = e.message ?: "连接失败"
                )
            }
        }
    }

    /**
     * 断开与树莓派的连接
     */
    fun disconnectFromRaspberryPi() {
        repository.disconnectFromRaspberryPi()
        _uiState.value = _uiState.value.copy(
            isConnecting = false,
            isConnected = false,
            connectionError = null
        )
    }

    /**
     * 开始蓝牙扫描
     */
    fun startBluetoothScan(duration: Int = 30) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(scanError = null)
                
                val result = repository.startBluetoothScan(duration)
                
                if (result.isFailure) {
                    _uiState.value = _uiState.value.copy(
                        scanError = result.exceptionOrNull()?.message ?: "扫描启动失败"
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        currentScanId = result.getOrNull()
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "开始扫描时发生错误", e)
                _uiState.value = _uiState.value.copy(
                    scanError = e.message ?: "扫描启动失败"
                )
            }
        }
    }

    /**
     * 停止蓝牙扫描
     */
    fun stopBluetoothScan() {
        viewModelScope.launch {
            try {
                val scanId = _uiState.value.currentScanId
                if (scanId != null) {
                    val result = repository.stopBluetoothScan(scanId)
                    
                    if (result.isFailure) {
                        _uiState.value = _uiState.value.copy(
                            scanError = result.exceptionOrNull()?.message ?: "停止扫描失败"
                        )
                    } else {
                        _uiState.value = _uiState.value.copy(
                            currentScanId = null
                        )
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "停止扫描时发生错误", e)
                _uiState.value = _uiState.value.copy(
                    scanError = e.message ?: "停止扫描失败"
                )
            }
        }
    }

    /**
     * 连接到设备
     */
    fun connectToDevice(deviceAddress: String) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(
                    connectingDevices = _uiState.value.connectingDevices + deviceAddress
                )
                
                val result = repository.connectToDevice(deviceAddress)
                
                _uiState.value = _uiState.value.copy(
                    connectingDevices = _uiState.value.connectingDevices - deviceAddress
                )
                
                if (result.isFailure) {
                    _uiState.value = _uiState.value.copy(
                        lastError = "连接设备失败: ${result.exceptionOrNull()?.message}",
                        showError = true
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "连接设备时发生错误", e)
                _uiState.value = _uiState.value.copy(
                    connectingDevices = _uiState.value.connectingDevices - deviceAddress,
                    lastError = "连接设备失败: ${e.message}",
                    showError = true
                )
            }
        }
    }

    /**
     * 断开设备连接
     */
    fun disconnectFromDevice(deviceAddress: String) {
        viewModelScope.launch {
            try {
                val result = repository.disconnectFromDevice(deviceAddress)
                
                if (result.isFailure) {
                    _uiState.value = _uiState.value.copy(
                        lastError = "断开设备失败: ${result.exceptionOrNull()?.message}",
                        showError = true
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "断开设备时发生错误", e)
                _uiState.value = _uiState.value.copy(
                    lastError = "断开设备失败: ${e.message}",
                    showError = true
                )
            }
        }
    }

    /**
     * 控制设备开关
     */
    fun toggleDevice(deviceAddress: String, deviceType: DeviceType, currentStatus: Boolean) {
        viewModelScope.launch {
            try {
                val command = if (currentStatus) "00" else "01" // 0关闭，1开启
                
                _uiState.value = _uiState.value.copy(
                    controllingDevices = _uiState.value.controllingDevices + deviceAddress
                )
                
                val result = repository.controlDevice(deviceAddress, deviceType.code, command)
                
                _uiState.value = _uiState.value.copy(
                    controllingDevices = _uiState.value.controllingDevices - deviceAddress
                )
                
                if (result.isFailure) {
                    _uiState.value = _uiState.value.copy(
                        lastError = "控制设备失败: ${result.exceptionOrNull()?.message}",
                        showError = true
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "控制设备时发生错误", e)
                _uiState.value = _uiState.value.copy(
                    controllingDevices = _uiState.value.controllingDevices - deviceAddress,
                    lastError = "控制设备失败: ${e.message}",
                    showError = true
                )
            }
        }
    }

    /**
     * 刷新系统信息
     */
    fun refreshSystemInfo() {
        viewModelScope.launch {
            try {
                repository.refreshSystemInfo()
            } catch (e: Exception) {
                Log.e(TAG, "刷新系统信息时发生错误", e)
            }
        }
    }

    /**
     * 刷新已连接设备
     */
    fun refreshConnectedDevices() {
        viewModelScope.launch {
            try {
                repository.refreshConnectedDevices()
            } catch (e: Exception) {
                Log.e(TAG, "刷新已连接设备时发生错误", e)
            }
        }
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(
            showError = false,
            lastError = null,
            connectionError = null,
            scanError = null
        )
    }

    /**
     * 处理蓝牙事件
     */
    private fun handleBluetoothEvent(event: BluetoothEvent) {
        when (event) {
            is BluetoothEvent.DeviceFound -> {
                Log.d(TAG, "发现新设备: ${event.device.address}")
                // 设备发现事件由repository自动更新discoveredDevices
            }
            is BluetoothEvent.DeviceConnected -> {
                Log.d(TAG, "设备已连接: ${event.device.address}")
                // 设备连接事件，刷新连接设备列表
                refreshConnectedDevices()
            }
            is BluetoothEvent.DeviceDisconnected -> {
                Log.d(TAG, "设备已断开: ${event.deviceAddress}")
                // 设备断开事件，刷新连接设备列表
                refreshConnectedDevices()
            }
            is BluetoothEvent.DeviceStatusChanged -> {
                Log.d(TAG, "设备状态变化: ${event.deviceAddress}")
                // 设备状态变化由repository自动更新
            }
            is BluetoothEvent.ScanStateChanged -> {
                Log.d(TAG, "扫描状态变化: ${event.isScanning}")
                // 扫描状态变化由repository自动更新scanStatus
            }
        }
    }

    /**
     * 开始心跳
     */
    private fun startHeartbeat() {
        viewModelScope.launch {
            while (connectionState.value == ConnectionState.CONNECTED) {
                repository.sendHeartbeat()
                delay(HEARTBEAT_INTERVAL)
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        repository.disconnectFromRaspberryPi()
    }
}

/**
 * UI状态数据类
 */
data class RemoteBluetoothUiState(
    val isConnecting: Boolean = false,
    val isConnected: Boolean = false,
    val connectionError: String? = null,
    val isScanning: Boolean = false,
    val scanError: String? = null,
    val currentScanId: String? = null,
    val connectingDevices: Set<String> = emptySet(),
    val controllingDevices: Set<String> = emptySet(),
    val showError: Boolean = false,
    val lastError: String? = null
)
