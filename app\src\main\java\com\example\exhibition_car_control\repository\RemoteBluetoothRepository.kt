package com.example.exhibition_car_control.repository

import android.util.Log
import com.example.exhibition_car_control.model.*
import com.example.exhibition_car_control.network.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 远程蓝牙仓库，负责管理与树莓派的通信
 */
class RemoteBluetoothRepository {
    
    companion object {
        private const val TAG = "RemoteBluetoothRepository"
        
        @Volatile
        private var INSTANCE: RemoteBluetoothRepository? = null
        
        fun getInstance(): RemoteBluetoothRepository {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: RemoteBluetoothRepository().also { INSTANCE = it }
            }
        }
    }

    private val webSocketManager = WebSocketManager()
    
    // 状态管理
    private val _connectionState = MutableStateFlow(ConnectionState.DISCONNECTED)
    val connectionState: StateFlow<ConnectionState> = _connectionState.asStateFlow()
    
    private val _connectedDevices = MutableStateFlow<List<RemoteBleDeviceInfo>>(emptyList())
    val connectedDevices: StateFlow<List<RemoteBleDeviceInfo>> = _connectedDevices.asStateFlow()
    
    private val _discoveredDevices = MutableStateFlow<List<RemoteBleDeviceInfo>>(emptyList())
    val discoveredDevices: StateFlow<List<RemoteBleDeviceInfo>> = _discoveredDevices.asStateFlow()
    
    private val _systemInfo = MutableStateFlow<SystemInfo?>(null)
    val systemInfo: StateFlow<SystemInfo?> = _systemInfo.asStateFlow()
    
    private val _scanStatus = MutableStateFlow(ScanStatus.IDLE)
    val scanStatus: StateFlow<ScanStatus> = _scanStatus.asStateFlow()
    
    private val _currentConnection = MutableStateFlow<RaspberryPiConnection?>(null)
    val currentConnection: StateFlow<RaspberryPiConnection?> = _currentConnection.asStateFlow()

    // 事件流
    val bluetoothEvents: Flow<BluetoothEvent> = webSocketManager.bluetoothEvents
    val systemEvents: Flow<SystemEvent> = webSocketManager.systemEvents
    val webSocketConnectionState: Flow<ConnectionState> = webSocketManager.connectionState
    val errorEvents: Flow<String> = webSocketManager.errorEvents

    /**
     * 连接到树莓派
     */
    suspend fun connectToRaspberryPi(address: String, port: Int = 8080): Result<RaspberryPiConnection> {
        return try {
            Log.d(TAG, "连接到树莓派: $address:$port")
            _connectionState.value = ConnectionState.CONNECTING
            
            // 初始化网络连接
            NetworkManager.initialize(address, port)
            
            // 测试HTTP连接
            val isHttpConnected = NetworkManager.testConnection()
            if (!isHttpConnected) {
                _connectionState.value = ConnectionState.ERROR
                return Result.failure(Exception("HTTP连接失败"))
            }
            
            // 建立WebSocket连接
            webSocketManager.connect(address, port)
            
            // 创建连接信息
            val connection = RaspberryPiConnection(
                id = "rpi_${System.currentTimeMillis()}",
                address = address,
                name = "RaspberryPi-$address",
                port = port,
                connectionType = ConnectionType.WIFI,
                state = ConnectionState.CONNECTED,
                connectedAt = System.currentTimeMillis()
            )
            
            _currentConnection.value = connection
            _connectionState.value = ConnectionState.CONNECTED
            
            // 获取初始状态
            refreshSystemInfo()
            refreshConnectedDevices()
            
            Log.d(TAG, "成功连接到树莓派")
            Result.success(connection)
            
        } catch (e: Exception) {
            Log.e(TAG, "连接树莓派失败", e)
            _connectionState.value = ConnectionState.ERROR
            Result.failure(e)
        }
    }

    /**
     * 断开与树莓派的连接
     */
    fun disconnectFromRaspberryPi() {
        Log.d(TAG, "断开与树莓派的连接")
        webSocketManager.disconnect()
        NetworkManager.cleanup()
        
        _connectionState.value = ConnectionState.DISCONNECTED
        _currentConnection.value = null
        _connectedDevices.value = emptyList()
        _discoveredDevices.value = emptyList()
        _systemInfo.value = null
        _scanStatus.value = ScanStatus.IDLE
    }

    /**
     * 开始蓝牙扫描
     */
    suspend fun startBluetoothScan(duration: Int = 30, filters: ScanFilters? = null): Result<String> {
        return try {
            val apiService = NetworkManager.getApiService() 
                ?: return Result.failure(Exception("网络连接未初始化"))
            
            val request = ScanRequest(duration = duration, filters = filters)
            val response = apiService.startBluetoothScan(request)
            
            if (response.isSuccessful && response.body()?.success == true) {
                val scanId = response.body()?.data?.scanId ?: ""
                _scanStatus.value = ScanStatus.SCANNING
                _discoveredDevices.value = emptyList() // 清空之前的扫描结果
                Log.d(TAG, "开始蓝牙扫描，扫描ID: $scanId")
                Result.success(scanId)
            } else {
                val errorMsg = response.body()?.message ?: "扫描启动失败"
                Log.e(TAG, "扫描启动失败: $errorMsg")
                Result.failure(Exception(errorMsg))
            }
        } catch (e: Exception) {
            Log.e(TAG, "开始扫描时发生错误", e)
            Result.failure(e)
        }
    }

    /**
     * 停止蓝牙扫描
     */
    suspend fun stopBluetoothScan(scanId: String): Result<String> {
        return try {
            val apiService = NetworkManager.getApiService() 
                ?: return Result.failure(Exception("网络连接未初始化"))
            
            val request = ScanStopRequest(scanId = scanId)
            val response = apiService.stopBluetoothScan(request)
            
            if (response.isSuccessful && response.body()?.success == true) {
                _scanStatus.value = ScanStatus.COMPLETED
                Log.d(TAG, "停止蓝牙扫描成功")
                Result.success("扫描已停止")
            } else {
                val errorMsg = response.body()?.message ?: "停止扫描失败"
                Log.e(TAG, "停止扫描失败: $errorMsg")
                Result.failure(Exception(errorMsg))
            }
        } catch (e: Exception) {
            Log.e(TAG, "停止扫描时发生错误", e)
            Result.failure(e)
        }
    }

    /**
     * 获取扫描结果
     */
    suspend fun getScanResults(): Result<List<RemoteBleDeviceInfo>> {
        return try {
            val apiService = NetworkManager.getApiService() 
                ?: return Result.failure(Exception("网络连接未初始化"))
            
            val response = apiService.getScanResults()
            
            if (response.isSuccessful && response.body()?.success == true) {
                val devices = response.body()?.data?.devices ?: emptyList()
                _discoveredDevices.value = devices
                Log.d(TAG, "获取扫描结果成功，发现 ${devices.size} 个设备")
                Result.success(devices)
            } else {
                val errorMsg = response.body()?.message ?: "获取扫描结果失败"
                Log.e(TAG, "获取扫描结果失败: $errorMsg")
                Result.failure(Exception(errorMsg))
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取扫描结果时发生错误", e)
            Result.failure(e)
        }
    }

    /**
     * 连接到设备
     */
    suspend fun connectToDevice(deviceAddress: String, timeout: Int = 30): Result<RemoteBleDeviceInfo> {
        return try {
            val apiService = NetworkManager.getApiService() 
                ?: return Result.failure(Exception("网络连接未初始化"))
            
            val request = ConnectRequest(timeout = timeout, autoRetry = true)
            val response = apiService.connectToDevice(deviceAddress, request)
            
            if (response.isSuccessful && response.body()?.success == true) {
                val deviceInfo = response.body()?.data?.deviceInfo
                if (deviceInfo != null) {
                    // 更新连接设备列表
                    refreshConnectedDevices()
                    Log.d(TAG, "连接设备成功: $deviceAddress")
                    Result.success(deviceInfo)
                } else {
                    Result.failure(Exception("设备信息为空"))
                }
            } else {
                val errorMsg = response.body()?.message ?: "连接设备失败"
                Log.e(TAG, "连接设备失败: $errorMsg")
                Result.failure(Exception(errorMsg))
            }
        } catch (e: Exception) {
            Log.e(TAG, "连接设备时发生错误", e)
            Result.failure(e)
        }
    }

    /**
     * 断开设备连接
     */
    suspend fun disconnectFromDevice(deviceAddress: String): Result<String> {
        return try {
            val apiService = NetworkManager.getApiService() 
                ?: return Result.failure(Exception("网络连接未初始化"))
            
            val response = apiService.disconnectFromDevice(deviceAddress)
            
            if (response.isSuccessful && response.body()?.success == true) {
                // 更新连接设备列表
                refreshConnectedDevices()
                Log.d(TAG, "断开设备连接成功: $deviceAddress")
                Result.success("设备已断开")
            } else {
                val errorMsg = response.body()?.message ?: "断开设备失败"
                Log.e(TAG, "断开设备失败: $errorMsg")
                Result.failure(Exception(errorMsg))
            }
        } catch (e: Exception) {
            Log.e(TAG, "断开设备时发生错误", e)
            Result.failure(e)
        }
    }

    /**
     * 控制设备
     */
    suspend fun controlDevice(
        deviceAddress: String, 
        deviceType: String, 
        command: String, 
        data: String = ""
    ): Result<DeviceStatus> {
        return try {
            val apiService = NetworkManager.getApiService() 
                ?: return Result.failure(Exception("网络连接未初始化"))
            
            val request = DeviceControlRequest(
                deviceType = deviceType,
                command = command,
                data = data
            )
            val response = apiService.controlDevice(deviceAddress, request)
            
            if (response.isSuccessful && response.body()?.success == true) {
                val deviceStatus = response.body()?.data?.status
                if (deviceStatus != null) {
                    // 更新设备状态
                    updateDeviceStatus(deviceAddress, deviceStatus)
                    Log.d(TAG, "控制设备成功: $deviceAddress")
                    Result.success(deviceStatus)
                } else {
                    Result.failure(Exception("设备状态为空"))
                }
            } else {
                val errorMsg = response.body()?.message ?: "控制设备失败"
                Log.e(TAG, "控制设备失败: $errorMsg")
                Result.failure(Exception(errorMsg))
            }
        } catch (e: Exception) {
            Log.e(TAG, "控制设备时发生错误", e)
            Result.failure(e)
        }
    }

    /**
     * 刷新系统信息
     */
    suspend fun refreshSystemInfo(): Result<SystemInfo> {
        return try {
            val apiService = NetworkManager.getApiService() 
                ?: return Result.failure(Exception("网络连接未初始化"))
            
            val response = apiService.getSystemStatus()
            
            if (response.isSuccessful && response.body()?.success == true) {
                val systemInfo = response.body()?.data
                if (systemInfo != null) {
                    _systemInfo.value = systemInfo
                    Log.d(TAG, "刷新系统信息成功")
                    Result.success(systemInfo)
                } else {
                    Result.failure(Exception("系统信息为空"))
                }
            } else {
                val errorMsg = response.body()?.message ?: "获取系统信息失败"
                Log.e(TAG, "获取系统信息失败: $errorMsg")
                Result.failure(Exception(errorMsg))
            }
        } catch (e: Exception) {
            Log.e(TAG, "刷新系统信息时发生错误", e)
            Result.failure(e)
        }
    }

    /**
     * 刷新已连接设备列表
     */
    suspend fun refreshConnectedDevices(): Result<List<RemoteBleDeviceInfo>> {
        return try {
            val apiService = NetworkManager.getApiService() 
                ?: return Result.failure(Exception("网络连接未初始化"))
            
            val response = apiService.getConnectedDevices()
            
            if (response.isSuccessful && response.body()?.success == true) {
                val devices = response.body()?.data?.devices ?: emptyList()
                _connectedDevices.value = devices
                Log.d(TAG, "刷新已连接设备成功，共 ${devices.size} 个设备")
                Result.success(devices)
            } else {
                val errorMsg = response.body()?.message ?: "获取已连接设备失败"
                Log.e(TAG, "获取已连接设备失败: $errorMsg")
                Result.failure(Exception(errorMsg))
            }
        } catch (e: Exception) {
            Log.e(TAG, "刷新已连接设备时发生错误", e)
            Result.failure(e)
        }
    }

    /**
     * 更新设备状态
     */
    private fun updateDeviceStatus(deviceAddress: String, newStatus: DeviceStatus) {
        val currentDevices = _connectedDevices.value.toMutableList()
        val deviceIndex = currentDevices.indexOfFirst { it.address == deviceAddress }
        
        if (deviceIndex >= 0) {
            val updatedDevice = currentDevices[deviceIndex].copy(
                status = newStatus,
                lastStatusUpdate = System.currentTimeMillis()
            )
            currentDevices[deviceIndex] = updatedDevice
            _connectedDevices.value = currentDevices
        }
    }

    /**
     * 发送WebSocket控制指令
     */
    fun sendWebSocketControl(deviceAddress: String, deviceType: String, command: String, data: String = "") {
        webSocketManager.sendDeviceControl(deviceAddress, deviceType, command, data)
    }

    /**
     * 发送心跳
     */
    fun sendHeartbeat() {
        webSocketManager.sendHeartbeat()
    }

    /**
     * 请求状态同步
     */
    fun requestSync() {
        webSocketManager.requestSync()
    }

    /**
     * 检查是否已连接
     */
    fun isConnected(): Boolean {
        return _connectionState.value == ConnectionState.CONNECTED && webSocketManager.isConnected()
    }
}
