package com.example.exhibition_car_control.network

import android.util.Log
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

/**
 * 网络管理器，负责创建和管理网络连接
 */
object NetworkManager {
    
    private const val TAG = "NetworkManager"
    private const val DEFAULT_TIMEOUT = 30L
    
    private var currentBaseUrl: String? = null
    private var retrofit: Retrofit? = null
    private var apiService: RaspberryPiApiService? = null

    /**
     * 初始化网络连接
     */
    fun initialize(raspberryPiAddress: String, port: Int = 8080) {
        val baseUrl = "http://$raspberryPiAddress:$port/"
        
        if (currentBaseUrl == baseUrl) {
            Log.d(TAG, "网络连接已存在，无需重新初始化")
            return
        }
        
        Log.d(TAG, "初始化网络连接: $baseUrl")
        currentBaseUrl = baseUrl
        
        // 创建HTTP客户端
        val httpClient = createHttpClient()
        
        // 创建Retrofit实例
        retrofit = Retrofit.Builder()
            .baseUrl(baseUrl)
            .client(httpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
        
        // 创建API服务
        apiService = retrofit?.create(RaspberryPiApiService::class.java)
        
        Log.d(TAG, "网络连接初始化完成")
    }

    /**
     * 创建HTTP客户端
     */
    private fun createHttpClient(): OkHttpClient {
        val loggingInterceptor = HttpLoggingInterceptor { message ->
            Log.d("HTTP", message)
        }.apply {
            level = HttpLoggingInterceptor.Level.BODY
        }

        return OkHttpClient.Builder()
            .connectTimeout(DEFAULT_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(DEFAULT_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(DEFAULT_TIMEOUT, TimeUnit.SECONDS)
            .addInterceptor(loggingInterceptor)
            .addInterceptor { chain ->
                val request = chain.request().newBuilder()
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Accept", "application/json")
                    .addHeader("User-Agent", "ZeroSense-Mobile-App")
                    .build()
                chain.proceed(request)
            }
            .build()
    }

    /**
     * 获取API服务实例
     */
    fun getApiService(): RaspberryPiApiService? {
        if (apiService == null) {
            Log.w(TAG, "API服务未初始化，请先调用initialize()方法")
        }
        return apiService
    }

    /**
     * 检查是否已初始化
     */
    fun isInitialized(): Boolean {
        return apiService != null
    }

    /**
     * 获取当前基础URL
     */
    fun getCurrentBaseUrl(): String? {
        return currentBaseUrl
    }

    /**
     * 清理网络连接
     */
    fun cleanup() {
        Log.d(TAG, "清理网络连接")
        currentBaseUrl = null
        retrofit = null
        apiService = null
    }

    /**
     * 测试网络连接
     */
    suspend fun testConnection(): Boolean {
        return try {
            val response = apiService?.getSystemStatus()
            response?.isSuccessful == true
        } catch (e: Exception) {
            Log.e(TAG, "网络连接测试失败", e)
            false
        }
    }
}
