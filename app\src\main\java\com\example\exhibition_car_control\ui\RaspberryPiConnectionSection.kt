package com.example.exhibition_car_control.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.exhibition_car_control.model.*
import com.example.exhibition_car_control.viewmodel.RemoteBluetoothUiState

/**
 * 树莓派连接管理界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RaspberryPiConnectionSection(
    uiState: RemoteBluetoothUiState,
    connectionState: ConnectionState,
    currentConnection: RaspberryPiConnection?,
    systemInfo: SystemInfo?,
    onConnect: (String, Int) -> Unit,
    onDisconnect: () -> Unit,
    modifier: Modifier = Modifier
) {
    var ipAddress by remember { mutableStateOf("*************") }
    var port by remember { mutableStateOf("8080") }
    var showConnectionDialog by remember { mutableStateOf(false) }

    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = when (connectionState) {
                ConnectionState.CONNECTED -> Color(0xFFE8F5E8)
                ConnectionState.ERROR -> Color(0xFFFFEBEE)
                else -> MaterialTheme.colorScheme.surface
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 标题栏
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "🖥️ 树莓派连接",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
                
                // 连接状态指示器
                ConnectionStatusIndicator(connectionState)
            }

            // 连接信息显示
            if (currentConnection != null) {
                ConnectedRaspberryPiInfo(
                    connection = currentConnection,
                    systemInfo = systemInfo,
                    onDisconnect = onDisconnect
                )
            } else {
                DisconnectedState(
                    uiState = uiState,
                    onShowConnectionDialog = { showConnectionDialog = true }
                )
            }

            // 连接对话框
            if (showConnectionDialog) {
                ConnectionDialog(
                    ipAddress = ipAddress,
                    port = port,
                    isConnecting = uiState.isConnecting,
                    onIpAddressChange = { ipAddress = it },
                    onPortChange = { port = it },
                    onConnect = { 
                        val portInt = port.toIntOrNull() ?: 8080
                        onConnect(ipAddress, portInt)
                        showConnectionDialog = false
                    },
                    onDismiss = { showConnectionDialog = false }
                )
            }
        }
    }
}

/**
 * 连接状态指示器
 */
@Composable
private fun ConnectionStatusIndicator(connectionState: ConnectionState) {
    val (color, icon, text) = when (connectionState) {
        ConnectionState.CONNECTED -> Triple(Color(0xFF4CAF50), Icons.Default.CheckCircle, "已连接")
        ConnectionState.CONNECTING -> Triple(Color(0xFFFF9800), Icons.Default.Refresh, "连接中")
        ConnectionState.DISCONNECTED -> Triple(Color(0xFF9E9E9E), Icons.Default.Circle, "未连接")
        ConnectionState.ERROR -> Triple(Color(0xFFF44336), Icons.Default.Warning, "连接错误")
        ConnectionState.DISCONNECTING -> Triple(Color(0xFFFF9800), Icons.Default.Refresh, "断开中")
    }

    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = text,
            tint = color,
            modifier = Modifier.size(16.dp)
        )
        Text(
            text = text,
            fontSize = 12.sp,
            color = color,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 已连接的树莓派信息显示
 */
@Composable
private fun ConnectedRaspberryPiInfo(
    connection: RaspberryPiConnection,
    systemInfo: SystemInfo?,
    onDisconnect: () -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 基本连接信息
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = connection.name,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = "${connection.address}:${connection.port}",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Button(
                onClick = onDisconnect,
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFFF44336)
                ),
                modifier = Modifier.height(32.dp)
            ) {
                Text("断开", fontSize = 12.sp)
            }
        }

        // 系统信息
        if (systemInfo != null) {
            SystemInfoDisplay(systemInfo)
        }

        // 连接统计
        ConnectionStats(connection)
    }
}

/**
 * 系统信息显示
 */
@Composable
private fun SystemInfoDisplay(systemInfo: SystemInfo) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
        )
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            verticalArrangement = Arrangement.spacedBy(6.dp)
        ) {
            Text(
                text = "系统状态",
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                SystemMetric("CPU", "${systemInfo.cpuUsage.toInt()}%")
                SystemMetric("内存", "${systemInfo.memoryUsage.toInt()}%")
                SystemMetric("温度", "${systemInfo.temperature.toInt()}°C")
                SystemMetric("设备", "${systemInfo.connectedDeviceCount}")
            }
            
            // 蓝牙状态
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Icon(
                    imageVector = if (systemInfo.bluetoothAdapterState.enabled) Icons.Default.Bluetooth else Icons.Default.Close,
                    contentDescription = "蓝牙状态",
                    tint = if (systemInfo.bluetoothAdapterState.enabled) Color(0xFF2196F3) else Color(0xFF9E9E9E),
                    modifier = Modifier.size(16.dp)
                )
                Text(
                    text = if (systemInfo.bluetoothAdapterState.enabled) "蓝牙已启用" else "蓝牙已禁用",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                if (systemInfo.bluetoothAdapterState.scanning) {
                    Text(
                        text = "• 扫描中",
                        fontSize = 12.sp,
                        color = Color(0xFF4CAF50)
                    )
                }
            }
        }
    }
}

/**
 * 系统指标显示
 */
@Composable
private fun SystemMetric(label: String, value: String) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            fontSize = 14.sp,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
        Text(
            text = label,
            fontSize = 10.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 连接统计信息
 */
@Composable
private fun ConnectionStats(connection: RaspberryPiConnection) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        connection.connectedAt?.let { connectedAt ->
            val duration = (System.currentTimeMillis() - connectedAt) / 1000
            val minutes = duration / 60
            val seconds = duration % 60
            Text(
                text = "连接时长: ${minutes}分${seconds}秒",
                fontSize = 10.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        connection.latency?.let { latency ->
            Text(
                text = "延迟: ${latency}ms",
                fontSize = 10.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 未连接状态显示
 */
@Composable
private fun DisconnectedState(
    uiState: RemoteBluetoothUiState,
    onShowConnectionDialog: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Icon(
            imageVector = Icons.Default.Devices,
            contentDescription = "未连接",
            tint = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.size(48.dp)
        )
        
        Text(
            text = "未连接到树莓派",
            fontSize = 16.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Text(
            text = "请连接到树莓派以开始远程控制",
            fontSize = 12.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Button(
            onClick = onShowConnectionDialog,
            enabled = !uiState.isConnecting
        ) {
            if (uiState.isConnecting) {
                CircularProgressIndicator(
                    modifier = Modifier.size(16.dp),
                    strokeWidth = 2.dp
                )
                Spacer(modifier = Modifier.width(8.dp))
            }
            Text(if (uiState.isConnecting) "连接中..." else "连接树莓派")
        }
        
        // 错误信息显示
        uiState.connectionError?.let { error ->
            Text(
                text = error,
                fontSize = 12.sp,
                color = Color(0xFFF44336)
            )
        }
    }
}

/**
 * 连接对话框
 */
@Composable
private fun ConnectionDialog(
    ipAddress: String,
    port: String,
    isConnecting: Boolean,
    onIpAddressChange: (String) -> Unit,
    onPortChange: (String) -> Unit,
    onConnect: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("连接到树莓派") },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                OutlinedTextField(
                    value = ipAddress,
                    onValueChange = onIpAddressChange,
                    label = { Text("IP地址") },
                    placeholder = { Text("*************") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    modifier = Modifier.fillMaxWidth()
                )
                
                OutlinedTextField(
                    value = port,
                    onValueChange = onPortChange,
                    label = { Text("端口") },
                    placeholder = { Text("8080") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    modifier = Modifier.fillMaxWidth()
                )
            }
        },
        confirmButton = {
            Button(
                onClick = onConnect,
                enabled = !isConnecting && ipAddress.isNotBlank() && port.isNotBlank()
            ) {
                if (isConnecting) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                }
                Text(if (isConnecting) "连接中..." else "连接")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}
