package com.example.exhibition_car_control.network

import com.example.exhibition_car_control.model.*
import retrofit2.Response
import retrofit2.http.*

/**
 * 树莓派API服务接口
 */
interface RaspberryPiApiService {

    // ==================== 系统状态接口 ====================

    /**
     * 获取系统状态
     */
    @GET("api/system/status")
    suspend fun getSystemStatus(): Response<ApiResponse<SystemInfo>>

    /**
     * 获取蓝牙适配器状态
     */
    @GET("api/bluetooth/adapter/status")
    suspend fun getBluetoothAdapterStatus(): Response<ApiResponse<BluetoothAdapterState>>

    /**
     * 获取服务状态
     */
    @GET("api/services/status")
    suspend fun getServicesStatus(): Response<ApiResponse<Map<String, String>>>

    // ==================== 蓝牙扫描接口 ====================

    /**
     * 开始蓝牙扫描
     */
    @POST("api/bluetooth/scan/start")
    suspend fun startBluetoothScan(@Body request: ScanRequest): Response<ApiResponse<ScanStartResponse>>

    /**
     * 停止蓝牙扫描
     */
    @POST("api/bluetooth/scan/stop")
    suspend fun stopBluetoothScan(@Body request: ScanStopRequest): Response<ApiResponse<String>>

    /**
     * 获取扫描结果
     */
    @GET("api/bluetooth/scan/results")
    suspend fun getScanResults(@Query("scanId") scanId: String? = null): Response<ApiResponse<ScanResult>>

    /**
     * 获取扫描状态
     */
    @GET("api/bluetooth/scan/status")
    suspend fun getScanStatus(): Response<ApiResponse<ScanStatusResponse>>

    // ==================== 设备连接管理接口 ====================

    /**
     * 连接到指定设备
     */
    @POST("api/bluetooth/devices/{address}/connect")
    suspend fun connectToDevice(
        @Path("address") address: String,
        @Body request: ConnectRequest
    ): Response<ApiResponse<ConnectResponse>>

    /**
     * 断开指定设备
     */
    @POST("api/bluetooth/devices/{address}/disconnect")
    suspend fun disconnectFromDevice(@Path("address") address: String): Response<ApiResponse<String>>

    /**
     * 获取已连接设备列表
     */
    @GET("api/bluetooth/devices/connected")
    suspend fun getConnectedDevices(): Response<ApiResponse<DeviceListResponse>>

    /**
     * 获取已配对设备列表
     */
    @GET("api/bluetooth/devices/paired")
    suspend fun getPairedDevices(): Response<ApiResponse<DeviceListResponse>>

    /**
     * 自动连接设备
     */
    @POST("api/bluetooth/devices/auto-connect")
    suspend fun autoConnectDevices(@Body request: AutoConnectRequest): Response<ApiResponse<AutoConnectResponse>>

    // ==================== 设备控制接口 ====================

    /**
     * 控制设备
     */
    @POST("api/devices/{address}/control")
    suspend fun controlDevice(
        @Path("address") address: String,
        @Body request: DeviceControlRequest
    ): Response<ApiResponse<DeviceControlResponse>>

    /**
     * 获取设备状态
     */
    @GET("api/devices/{address}/status")
    suspend fun getDeviceStatus(@Path("address") address: String): Response<ApiResponse<DeviceStatus>>

    /**
     * 查询设备状态
     */
    @POST("api/devices/{address}/query")
    suspend fun queryDeviceStatus(
        @Path("address") address: String,
        @Body request: DeviceQueryRequest
    ): Response<ApiResponse<DeviceStatus>>

    /**
     * 获取所有设备状态
     */
    @GET("api/devices/all-status")
    suspend fun getAllDevicesStatus(): Response<ApiResponse<AllDevicesStatusResponse>>

    // ==================== 日志接口 ====================

    /**
     * 获取最近日志
     */
    @GET("api/logs/recent")
    suspend fun getRecentLogs(
        @Query("level") level: String? = null,
        @Query("limit") limit: Int = 50,
        @Query("since") since: String? = null
    ): Response<ApiResponse<LogsResponse>>
}

// ==================== 请求数据模型 ====================

/**
 * 通用API响应格式
 */
data class ApiResponse<T>(
    val requestId: String? = null,
    val timestamp: String,
    val success: Boolean,
    val code: Int,
    val message: String,
    val data: T? = null,
    val error: OperationError? = null
)

/**
 * 扫描请求
 */
data class ScanRequest(
    val duration: Int = 30,
    val deviceTypes: List<String> = listOf("BLE"),
    val filters: ScanFilters? = null
)

/**
 * 扫描过滤器
 */
data class ScanFilters(
    val rssiThreshold: Int = -80,
    val namePattern: String? = null,
    val serviceUuids: List<String> = emptyList()
)

/**
 * 扫描开始响应
 */
data class ScanStartResponse(
    val scanId: String,
    val estimatedDuration: Int
)

/**
 * 扫描停止请求
 */
data class ScanStopRequest(
    val scanId: String
)

/**
 * 扫描状态响应
 */
data class ScanStatusResponse(
    val isScanning: Boolean,
    val scanId: String? = null,
    val elapsed: Int = 0,
    val remainingTime: Int = 0
)

/**
 * 连接请求
 */
data class ConnectRequest(
    val timeout: Int = 30,
    val autoRetry: Boolean = true,
    val priority: String = "normal"
)

/**
 * 连接响应
 */
data class ConnectResponse(
    val connectionId: String,
    val deviceInfo: RemoteBleDeviceInfo
)

/**
 * 设备列表响应
 */
data class DeviceListResponse(
    val devices: List<RemoteBleDeviceInfo>,
    val totalCount: Int
)

/**
 * 自动连接请求
 */
data class AutoConnectRequest(
    val deviceAddresses: List<String> = emptyList(),
    val strategy: String = "priority",
    val timeout: Int = 30
)

/**
 * 自动连接响应
 */
data class AutoConnectResponse(
    val connectedCount: Int,
    val failedDevices: List<String>,
    val connectedDevices: List<RemoteBleDeviceInfo>
)

/**
 * 设备控制请求
 */
data class DeviceControlRequest(
    val deviceType: String,
    val command: String,
    val data: String = "",
    val timeout: Int = 5000
)

/**
 * 设备控制响应
 */
data class DeviceControlResponse(
    val response: String,
    val status: DeviceStatus,
    val executionTime: Long
)

/**
 * 设备查询请求
 */
data class DeviceQueryRequest(
    val deviceType: String,
    val queryType: String = "status"
)

/**
 * 所有设备状态响应
 */
data class AllDevicesStatusResponse(
    val devices: List<DeviceStatusInfo>
)

/**
 * 设备状态信息
 */
data class DeviceStatusInfo(
    val address: String,
    val name: String?,
    val type: String,
    val status: DeviceStatus,
    val connectionState: String
)

/**
 * 日志响应
 */
data class LogsResponse(
    val logs: List<LogEntry>,
    val totalCount: Int
)

/**
 * 日志条目
 */
data class LogEntry(
    val timestamp: String,
    val level: String,
    val message: String,
    val source: String? = null,
    val details: String? = null
)
