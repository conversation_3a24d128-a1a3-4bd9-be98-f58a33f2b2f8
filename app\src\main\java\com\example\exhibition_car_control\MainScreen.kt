package com.example.exhibition_car_control

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
// import com.example.exhibition_car_control.config.DeviceConfig
import com.example.exhibition_car_control.model.ConnectionState
import com.example.exhibition_car_control.ui.RaspberryPiConnectionSection
import com.example.exhibition_car_control.ui.RemoteDeviceControlSection
import com.example.exhibition_car_control.viewmodel.RemoteBluetoothViewModel

/**
 * 主界面，集成本地蓝牙控制和远程控制功能
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(modifier: Modifier = Modifier) {
    val remoteViewModel: RemoteBluetoothViewModel = viewModel()
    
    // 收集状态
    val uiState by remoteViewModel.uiState.collectAsState()
    val connectionState by remoteViewModel.connectionState.collectAsState()
    val connectedDevices by remoteViewModel.connectedDevices.collectAsState()
    val discoveredDevices by remoteViewModel.discoveredDevices.collectAsState()
    val systemInfo by remoteViewModel.systemInfo.collectAsState()
    val scanStatus by remoteViewModel.scanStatus.collectAsState()
    val currentConnection by remoteViewModel.currentConnection.collectAsState()

    // 控制模式状态
    var controlMode by remember { mutableStateOf(ControlMode.LOCAL) }
    
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 标题和模式切换
        item {
            Column(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Text(
                    text = "展车蓝牙控制",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2196F3)
                )

                Text(
                    text = "本地控制和远程控制",
                    fontSize = 16.sp,
                    color = Color.Gray
                )

                // 控制模式切换
                ControlModeSelector(
                    currentMode = controlMode,
                    onModeChange = { controlMode = it }
                )
            }
        }

        // 错误信息显示
        if (uiState.showError && uiState.lastError != null) {
            item {
                Card(
                    colors = CardDefaults.cardColors(containerColor = Color.Red.copy(alpha = 0.1f))
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(12.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "错误: ${uiState.lastError}",
                            color = Color.Red,
                            modifier = Modifier.weight(1f)
                        )
                        IconButton(onClick = { remoteViewModel.clearError() }) {
                            Icon(
                                imageVector = Icons.Default.Close,
                                contentDescription = "关闭",
                                tint = Color.Red
                            )
                        }
                    }
                }
            }
        }

        // 根据控制模式显示不同内容
        when (controlMode) {
            ControlMode.LOCAL -> {
                // 本地蓝牙控制界面
                item {
                    BluetoothDemoScreen()
                }
            }
            ControlMode.REMOTE -> {
                // 远程控制界面
                item {
                    RaspberryPiConnectionSection(
                        uiState = uiState,
                        connectionState = connectionState,
                        currentConnection = currentConnection,
                        systemInfo = systemInfo,
                        onConnect = { address, port ->
                            remoteViewModel.connectToRaspberryPi(address, port)
                        },
                        onDisconnect = {
                            remoteViewModel.disconnectFromRaspberryPi()
                        }
                    )
                }

                // 只有在连接成功后才显示设备控制界面
                if (connectionState == ConnectionState.CONNECTED) {
                    item {
                        RemoteDeviceControlSection(
                            uiState = uiState,
                            connectedDevices = connectedDevices,
                            discoveredDevices = discoveredDevices,
                            scanStatus = scanStatus,
                            onStartScan = {
                                remoteViewModel.startBluetoothScan()
                            },
                            onStopScan = {
                                remoteViewModel.stopBluetoothScan()
                            },
                            onConnectDevice = { deviceAddress ->
                                remoteViewModel.connectToDevice(deviceAddress)
                            },
                            onDisconnectDevice = { deviceAddress ->
                                remoteViewModel.disconnectFromDevice(deviceAddress)
                            },
                            onToggleDevice = { deviceAddress, deviceType, isOn ->
                                remoteViewModel.toggleDevice(deviceAddress, deviceType, isOn)
                            }
                        )
                    }
                }
            }
        }

        // 调试信息
        item {
            Card(
                colors = CardDefaults.cardColors(containerColor = Color.Yellow.copy(alpha = 0.1f))
            ) {
                Column(
                    modifier = Modifier.padding(8.dp),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    Text(
                        text = "调试信息:",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF795548)
                    )
                    Text(
                        text = "控制模式: ${controlMode.displayName}",
                        fontSize = 12.sp,
                        color = Color(0xFF795548)
                    )
                    if (controlMode == ControlMode.REMOTE) {
                        Text(
                            text = "连接状态: ${connectionState.name}",
                            fontSize = 12.sp,
                            color = Color(0xFF795548)
                        )
                        Text(
                            text = "已连接设备: ${connectedDevices.size}",
                            fontSize = 12.sp,
                            color = Color(0xFF795548)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 控制模式选择器
 */
@Composable
private fun ControlModeSelector(
    currentMode: ControlMode,
    onModeChange: (ControlMode) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "🎛️ 控制模式",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF2196F3)
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                ControlMode.values().forEach { mode ->
                    @OptIn(ExperimentalMaterial3Api::class)
                FilterChip(
                        onClick = { onModeChange(mode) },
                        label = { 
                            Text(
                                text = mode.displayName,
                                fontSize = 14.sp
                            )
                        },
                        selected = currentMode == mode,
                        leadingIcon = {
                            Icon(
                                imageVector = mode.icon,
                                contentDescription = mode.displayName,
                                modifier = Modifier.size(16.dp)
                            )
                        },
                        modifier = Modifier.weight(1f)
                    )
                }
            }
            
            // 模式说明
            Text(
                text = when (currentMode) {
                    ControlMode.LOCAL -> "直接控制本设备的蓝牙功能，适用于手机直接连接BLE设备"
                    ControlMode.REMOTE -> "通过网络连接到树莓派，远程控制树莓派的蓝牙功能"
                },
                fontSize = 12.sp,
                color = Color.Gray
            )
        }
    }
}

/**
 * 控制模式枚举
 */
enum class ControlMode(
    val displayName: String,
    val icon: androidx.compose.ui.graphics.vector.ImageVector
) {
    LOCAL("本地控制", Icons.Default.Phone),
    REMOTE("远程控制", Icons.Default.Wifi)
}
