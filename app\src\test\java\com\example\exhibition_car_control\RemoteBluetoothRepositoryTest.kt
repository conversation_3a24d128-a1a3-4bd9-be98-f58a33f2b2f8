package com.example.exhibition_car_control

import com.example.exhibition_car_control.model.*
import com.example.exhibition_car_control.repository.RemoteBluetoothRepository
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.Assert.*

/**
 * 远程蓝牙仓库测试
 */
class RemoteBluetoothRepositoryTest {

    @Test
    fun testRepositoryInitialization() {
        val repository = RemoteBluetoothRepository.getInstance()
        assertNotNull(repository)
        
        // 验证初始状态
        assertEquals(ConnectionState.DISCONNECTED, repository.connectionState.value)
        assertTrue(repository.connectedDevices.value.isEmpty())
        assertTrue(repository.discoveredDevices.value.isEmpty())
        assertNull(repository.systemInfo.value)
        assertEquals(ScanStatus.IDLE, repository.scanStatus.value)
        assertNull(repository.currentConnection.value)
    }

    @Test
    fun testSingletonPattern() {
        val repository1 = RemoteBluetoothRepository.getInstance()
        val repository2 = RemoteBluetoothRepository.getInstance()
        
        // 验证单例模式
        assertSame(repository1, repository2)
    }

    @Test
    fun testConnectionStateFlow() = runTest {
        val repository = RemoteBluetoothRepository.getInstance()
        
        // 验证初始连接状态
        assertEquals(ConnectionState.DISCONNECTED, repository.connectionState.value)
        
        // 验证未连接状态
        assertFalse(repository.isConnected())
    }

    @Test
    fun testDeviceTypeEnum() {
        // 测试设备类型枚举
        assertEquals("02", DeviceType.WIRELESS_CHARGER.code)
        assertEquals("03", DeviceType.MAIN_LAMP.code)
        assertEquals("04", DeviceType.AMBIENT_LAMP.code)
        assertEquals("05", DeviceType.FRAGRANCE_MACHINE.code)
        assertEquals("06", DeviceType.FAN.code)
        assertEquals("99", DeviceType.BUTTON_DEVICE.code)
        
        // 测试从代码获取设备类型
        assertEquals(DeviceType.WIRELESS_CHARGER, DeviceType.fromCode("02"))
        assertEquals(DeviceType.MAIN_LAMP, DeviceType.fromCode("03"))
        assertEquals(DeviceType.UNKNOWN, DeviceType.fromCode("99999"))
    }

    @Test
    fun testConnectionStateEnum() {
        // 测试连接状态枚举
        val states = ConnectionState.values()
        assertTrue(states.contains(ConnectionState.DISCONNECTED))
        assertTrue(states.contains(ConnectionState.CONNECTING))
        assertTrue(states.contains(ConnectionState.CONNECTED))
        assertTrue(states.contains(ConnectionState.DISCONNECTING))
        assertTrue(states.contains(ConnectionState.ERROR))
    }

    @Test
    fun testScanStatusEnum() {
        // 测试扫描状态枚举
        val statuses = ScanStatus.values()
        assertTrue(statuses.contains(ScanStatus.IDLE))
        assertTrue(statuses.contains(ScanStatus.SCANNING))
        assertTrue(statuses.contains(ScanStatus.COMPLETED))
        assertTrue(statuses.contains(ScanStatus.ERROR))
        
        // 测试显示名称
        assertEquals("空闲", ScanStatus.IDLE.displayName)
        assertEquals("扫描中", ScanStatus.SCANNING.displayName)
        assertEquals("扫描完成", ScanStatus.COMPLETED.displayName)
        assertEquals("扫描错误", ScanStatus.ERROR.displayName)
    }

    @Test
    fun testBluetoothEventTypes() {
        val deviceInfo = RemoteBleDeviceInfo(
            address = "AA:BB:CC:DD:EE:FF",
            name = "Test Device",
            deviceType = "03",
            rssi = -50,
            isConnected = false,
            isPaired = false,
            discoveredAt = System.currentTimeMillis()
        )
        
        // 测试设备发现事件
        val deviceFoundEvent = BluetoothEvent.DeviceFound(deviceInfo, "scan123")
        assertEquals("device_found_AA:BB:CC:DD:EE:FF", deviceFoundEvent.eventId)
        assertEquals("scanner", deviceFoundEvent.source)
        
        // 测试设备连接事件
        val deviceConnectedEvent = BluetoothEvent.DeviceConnected(deviceInfo)
        assertEquals("device_connected_AA:BB:CC:DD:EE:FF", deviceConnectedEvent.eventId)
        assertEquals("connection_manager", deviceConnectedEvent.source)
        
        // 测试设备断开事件
        val deviceDisconnectedEvent = BluetoothEvent.DeviceDisconnected("AA:BB:CC:DD:EE:FF", "User requested")
        assertEquals("device_disconnected_AA:BB:CC:DD:EE:FF", deviceDisconnectedEvent.eventId)
        assertEquals("connection_manager", deviceDisconnectedEvent.source)
        
        // 测试扫描状态变化事件
        val scanStateChangedEvent = BluetoothEvent.ScanStateChanged(true, "scan123")
        assertEquals("scan_state_changed", scanStateChangedEvent.eventId)
        assertEquals("scanner", scanStateChangedEvent.source)
    }

    @Test
    fun testSystemEventTypes() {
        val connectionId = "conn123"
        val oldState = ConnectionState.CONNECTING
        val newState = ConnectionState.CONNECTED
        
        // 测试连接状态变化事件
        val connectionStateEvent = SystemEvent.ConnectionStateChanged(connectionId, oldState, newState)
        assertEquals("connection_state_$connectionId", connectionStateEvent.eventId)
        
        // 测试系统状态更新事件
        val systemInfo = SystemInfo(
            cpuUsage = 25.5,
            memoryUsage = 60.0,
            temperature = 45.0,
            connectedDeviceCount = 3,
            bluetoothAdapterState = BluetoothAdapterState(enabled = true, scanning = false),
            uptime = 3600000L
        )
        val systemStatusEvent = SystemEvent.SystemStatusUpdated(systemInfo)
        assertEquals("system_status", systemStatusEvent.eventId)
        
        // 测试错误事件
        val error = OperationError(code = "E001", message = "Connection failed", details = "Network timeout")
        val errorEvent = SystemEvent.ErrorOccurred(error, "connection_attempt")
        assertEquals("error_E001", errorEvent.eventId)
    }

    @Test
    fun testRaspberryPiConnection() {
        val connection = RaspberryPiConnection(
            id = "rpi_001",
            address = "*************",
            name = "RaspberryPi-100",
            port = 8080,
            connectionType = ConnectionType.WIFI,
            state = ConnectionState.CONNECTED,
            connectedAt = System.currentTimeMillis(),
            latency = 25
        )
        
        assertEquals("rpi_001", connection.id)
        assertEquals("*************", connection.address)
        assertEquals("RaspberryPi-100", connection.name)
        assertEquals(8080, connection.port)
        assertEquals(ConnectionType.WIFI, connection.connectionType)
        assertEquals(ConnectionState.CONNECTED, connection.state)
        assertEquals(25, connection.latency)
    }

    @Test
    fun testDeviceStatus() {
        val deviceStatus = DeviceStatus(
            isOn = true,
            brightness = 80,
            color = "#FF5733",
            speed = 3,
            temperature = 22.5,
            batteryLevel = 85,
            lastUpdate = System.currentTimeMillis()
        )
        
        assertTrue(deviceStatus.isOn)
        assertEquals(80, deviceStatus.brightness)
        assertEquals("#FF5733", deviceStatus.color)
        assertEquals(3, deviceStatus.speed)
        assertEquals(22.5, deviceStatus.temperature, 0.01)
        assertEquals(85, deviceStatus.batteryLevel)
    }

    @Test
    fun testOperationResult() {
        // 测试成功结果
        val successResult = OperationResult(
            success = true,
            message = "Operation completed successfully",
            data = "result_data",
            timestamp = System.currentTimeMillis()
        )
        
        assertTrue(successResult.success)
        assertEquals("Operation completed successfully", successResult.message)
        assertEquals("result_data", successResult.data)
        assertNull(successResult.error)
        
        // 测试失败结果
        val error = OperationError(code = "E002", message = "Operation failed")
        val failureResult = OperationResult(
            success = false,
            message = "Operation failed",
            error = error,
            timestamp = System.currentTimeMillis()
        )
        
        assertFalse(failureResult.success)
        assertEquals("Operation failed", failureResult.message)
        assertNull(failureResult.data)
        assertEquals(error, failureResult.error)
    }
}
